:root {
    /* Luxury metallics */
    --lucky-green: #36b898;  /* Refined emerald */
    --clover-green: #00a67c;  /* Deep jade */
    
    /* Premium precious metals */
    --fortune-gold: #d4af37;  /* Authentic gold */
    --fortune-platinum: #e5e4e2;  /* Platinum */
    --rose-gold: #b76e79;  /* Elegant rose gold */
    --white-gold: #e8e8e0;  /* White gold */
    
    /* Sophisticated jewel tones */
    --rainbow-1: #b91451;  /* Deep ruby */
    --rainbow-2: #c7a94a;  /* Antique gold */
    --rainbow-3: #1e3f8f;  /* Royal sapphire */
    --rainbow-4: #05846c;  /* Rich emerald */
    
    /* Luxury accent colors */
    --meme-pink: #8f1537;  /* Burgundy */
    --meme-purple: #4a1f68;  /* Royal purple */
    
    /* Refined highlights */
    --neon-blue: #75b6c3;  /* Ice blue */
    --neon-purple: #9370db;  /* Soft amethyst */
    
    /* Premium primary palette */
    --primary-color: #36b898;  /* Refined emerald */
    --primary-dark: #1a7a64;  /* Deep emerald */
    --primary-light: #98c9bb;  /* Soft jade */
    
    /* Luxury secondary colors */
    --secondary-color: #d4af37;  /* Authentic gold */
    --accent-color: #8f1537;  /* Burgundy */
    
    /* Sophisticated background */
    --background-color: #0c1f1d;  /* Deep forest */
    --text-color: #f5f5f0;  /* Soft pearl */
    --light-text: #98c9bb;  /* Soft jade */
    --white: #f8f8f2;  /* Pearl white */
    
    /* Premium gradients */
    --gradient-primary: linear-gradient(135deg, 
        #8f1537,  /* Burgundy */
        #d4af37,  /* Gold */
        #1e3f8f,  /* Sapphire */
        #36b898   /* Emerald */
    );
    --gradient-hover: linear-gradient(135deg, 
        #36b898,  /* Emerald */
        #1e3f8f,  /* Sapphire */
        #d4af37,  /* Gold */
        #8f1537   /* Burgundy */
    );
    --gradient-background: linear-gradient(45deg, 
        #0c1f1d 0%,  /* Deep forest */
        #0a1a18 100% /* Darker forest */
    );
    --holographic: linear-gradient(135deg, 
        rgba(229, 228, 226, 0.05) 0%,    /* Platinum */
        rgba(212, 175, 55, 0.15) 25%,    /* Gold */
        rgba(229, 228, 226, 0.05) 50%,   /* Platinum */
        rgba(212, 175, 55, 0.15) 75%,    /* Gold */
        rgba(229, 228, 226, 0.05) 100%   /* Platinum */
    );
}

* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Press Start 2P', 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, sans-serif;
    line-height: 1.6;
    color: var(--text-color);
    background: var(--gradient-background);
    overflow-x: hidden;
}

.container {
    max-width: 1400px;
    margin: 0 auto;
    padding: 0 40px;
}

/* Navbar */
.navbar {
    background: rgba(12, 42, 31, 0.15);
    backdrop-filter: blur(6px);
    -webkit-backdrop-filter: blur(6px);
    box-shadow: 0 2px 15px rgba(26, 255, 156, 0.08);
    border-bottom: 1px solid rgba(26, 255, 156, 0.08);
    position: fixed;
    width: 100%;
    top: 0;
    z-index: 10;
    padding: 5px 0;
    transition: all 0.3s ease;
}

.navbar .container {
    display: flex;
    justify-content: space-between;
    align-items: center;
    height: 50px;
}

.logo {
    font-size: 24px;
    font-weight: 800;
    background: var(--gradient-primary);
    -webkit-background-clip: text;
    background-clip: text;
    color: transparent;
    text-shadow: 0 0 10px rgba(0, 255, 136, 0.5);
    animation: rainbow-text 5s linear infinite;
    mix-blend-mode: overlay;
}

.nav-links {
    display: flex;
    list-style: none;
    gap: 30px;
}

.nav-links a {
    text-decoration: none;
    color: var(--text-color);
    font-weight: 500;
    transition: all 0.3s ease;
    text-shadow: 0 0 10px rgba(0, 0, 0, 0.2);
}

.nav-links a:hover {
    color: var(--primary-color);
    text-shadow: 0 0 10px var(--primary-color);
}

/* Hero Section */
.hero {
    padding: 32px 0;
    text-align: center;
    background: var(--gradient-background);
    position: relative;
    overflow: hidden;
    min-height: 57.6vh;
    display: flex;
    align-items: center;
    margin-top: 0;
}

.hero::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: var(--holographic);
    opacity: 0.1;
    animation: holographic-shift 3s linear infinite;
    pointer-events: none;
}

.hero .container {
    position: relative;
    z-index: 1;
    width: 100%;
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
}

/* Title container and tooltip */
.title-container {
    text-align: center;
    margin-bottom: 30px;
    max-width: 800px;
    margin-left: auto;
    margin-right: auto;
}

.title-container::before,
.title-container::after {
    display: none;
}

.title-container h1 {
    font-size: clamp(20px, 5vw, 34px);
    margin-bottom: 16px;
    color: var(--text-color);
    text-shadow: 0 0 10px var(--neon-blue),
                 0 0 20px var(--lucky-green),
                 0 0 30px var(--fortune-gold);
    animation: neon-pulse 1.5s ease-in-out infinite;
    letter-spacing: 2px;
    line-height: 1.4;
    background: none;
}

.title-container h1 span {
    display: inline-block;
}

/* Emoji special animation */
.title-container h1 span:last-child {
    display: inline-block;
    animation: lucky-clover 2s ease-in-out infinite;
    transform-origin: bottom center;
}

@keyframes lucky-bounce {
    0%, 100% {
        transform: translateY(0) scale(1) rotate(0);
        text-shadow: 0 4px 8px rgba(46, 204, 113, 0.2);
    }
    25% {
        transform: translateY(-15px) scale(1.1) rotate(-5deg);
        text-shadow: 0 8px 16px rgba(46, 204, 113, 0.3);
    }
    50% {
        transform: translateY(0) scale(0.95) rotate(0);
        text-shadow: 0 2px 4px rgba(46, 204, 113, 0.2);
    }
    75% {
        transform: translateY(-8px) scale(1.05) rotate(5deg);
        text-shadow: 0 6px 12px rgba(46, 204, 113, 0.25);
    }
}

@keyframes lucky-clover {
    0%, 100% {
        transform: rotate(0) scale(1);
    }
    25% {
        transform: rotate(15deg) scale(1.2);
    }
    50% {
        transform: rotate(-10deg) scale(1.1);
    }
    75% {
        transform: rotate(5deg) scale(1.3);
    }
}

/* Update tooltip to match the energetic style */
.title-tooltip {
    position: absolute;
    background: var(--gradient-primary);
    color: white;
    padding: 10px 20px;
    border-radius: 20px;
    font-size: 18px;
    bottom: 0;
    left: 50%;
    transform: translateX(-50%) translateY(10px);
    opacity: 0;
    visibility: hidden;
    transition: all 0.3s cubic-bezier(0.68, -0.55, 0.265, 1.55);
    white-space: nowrap;
    box-shadow: 0 4px 15px rgba(46, 204, 113, 0.3);
    font-weight: bold;
}

.title-container:hover .title-tooltip {
    opacity: 1;
    visibility: visible;
    transform: translateX(-50%) translateY(40px) scale(1.1);
}

.hero .subtitle {
    font-size: clamp(12px, 3vw, 16px);
    color: var(--light-text);
    margin-bottom: 24px;
    text-shadow: 0 0 10px rgba(0, 255, 136, 0.5);
    letter-spacing: 1.5px;
    max-width: min(800px, 90%);
    margin-left: auto;
    margin-right: auto;
}

/* Contract Address */
.contract-address {
    background: rgba(10, 31, 26, 0.8);
    position: relative;
    overflow: visible;
    padding: 20px 32px;
    border-radius: 50px;
    margin: 32px auto;
    max-width: min(800px, 90%);
    box-shadow: 0 0 40px rgba(24, 220, 255, 0.15);
    backdrop-filter: blur(10px);
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 16px;
}

#contract-address {
    font-family: 'Press Start 2P', monospace;
    color: var(--lucky-green);
    text-shadow: 0 0 8px var(--lucky-green);
    animation: text-flicker 3s linear infinite;
    font-size: clamp(9px, 2.5vw, 12px);
    letter-spacing: 1px;
    user-select: all;
    cursor: pointer;
    text-align: center;
    margin: 0;
    padding: 0;
    line-height: 1.5;
}

.copy-btn {
    background: rgba(24, 220, 255, 0.15);
    border: 2px solid var(--neon-blue);
    color: var(--neon-blue);
    cursor: pointer;
    padding: 10px;
    border-radius: 10px;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    justify-content: center;
    min-width: 38px;
    height: 38px;
    backdrop-filter: blur(5px);
    box-shadow: 0 0 15px rgba(24, 220, 255, 0.3);
    flex-shrink: 0;
}

.copy-btn i {
    font-size: 20px;
    text-shadow: 0 0 8px var(--neon-blue);
}

.copy-btn:hover {
    background: rgba(24, 220, 255, 0.25);
    transform: scale(1.1);
    box-shadow: 0 0 20px rgba(24, 220, 255, 0.5);
}

.copy-btn.copied {
    background: rgba(0, 255, 136, 0.25);
    border-color: var(--lucky-green);
    color: var(--lucky-green);
    box-shadow: 0 0 20px rgba(0, 255, 136, 0.5);
}

.copy-btn.copied i {
    text-shadow: 0 0 8px var(--lucky-green);
    animation: copy-success 0.5s ease;
}

.cta-buttons {
    display: flex;
    justify-content: center;
    gap: clamp(12px, 3vw, 20px);
    margin-top: 24px;
    padding: 0 clamp(10px, 2vw, 20px);
    flex-wrap: wrap;
}

.btn {
    background: var(--gradient-primary);
    color: var(--white);
    border: none;
    outline: none;
    padding: 0 32px;
    border-radius: 100px;
    font-weight: bold;
    text-transform: uppercase;
    position: relative;
    overflow: hidden;
    transition: all 0.3s ease;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);
    font-size: 14px;
    line-height: 1;
    letter-spacing: 1.5px;
    text-decoration: none;
    display: inline-flex;
    align-items: center;
    justify-content: center;
    gap: 12px;
    min-width: 160px;
    height: 48px;
    cursor: pointer;
}

.btn::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(
        120deg,
        transparent,
        rgba(255, 255, 255, 0.2),
        transparent
    );
    transform: translateX(-100%);
    transition: 0.6s;
}

.btn:hover::before {
    transform: translateX(100%);
}

.btn.primary {
    background: linear-gradient(135deg, var(--fortune-gold), var(--lucky-green), var(--fortune-gold));
    background-size: 200% 100%;
    animation: shine 3s linear infinite;
    box-shadow: 0 4px 20px rgba(212, 175, 55, 0.4),
                0 0 30px rgba(54, 184, 152, 0.3);
    border: 2px solid rgba(212, 175, 55, 0.3);
    transform: scale(1.05);
}

.btn.primary:hover {
    box-shadow: 0 6px 25px rgba(212, 175, 55, 0.5),
                0 0 40px rgba(54, 184, 152, 0.4);
    transform: scale(1.08);
}

@keyframes shine {
    0% { background-position: 200% 0; }
    100% { background-position: -200% 0; }
}

.btn.secondary {
    background: var(--lucky-green);
    box-shadow: 0 4px 15px rgba(46, 204, 113, 0.2);
    height: 48px;
}

/* Sections */
section {
    padding: 40px 0;
    position: relative;
    overflow: hidden;
}

section h2 {
    font-size: 36px;
    margin-bottom: 30px;
    text-align: center;
}

/* Features */
.features {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 30px;
    margin-top: 30px;
    padding: 0 20px;
}

.feature {
    background: rgba(10, 31, 26, 0.6);
    padding: 40px 30px;
    border-radius: 20px;
    text-align: center;
    transition: all 0.3s ease;
    position: relative;
    margin-bottom: 30px;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
}

.feature::before {
    content: '🍀';
    position: absolute;
    top: -20px;
    right: -20px;
    font-size: 40px;
    opacity: 0.1;
    transform: rotate(45deg);
}

.feature:hover {
    transform: translateY(-5px);
    box-shadow: 0 12px 40px rgba(46, 204, 113, 0.15);
}

.feature:hover::before {
    animation: float 3s ease-in-out infinite;
}

.feature i {
    background: var(--gradient-primary);
    -webkit-background-clip: text;
    background-clip: text;
    color: transparent;
    font-size: 44px;
    margin-bottom: 20px;
}

.feature h3 {
    margin-bottom: 16px;
    margin-top: 0;
}

.feature p {
    margin-bottom: 0;
    line-height: 1.6;
}

/* Feature Action Buttons */
.feature-actions {
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    display: flex;
    justify-content: space-between;
    padding: 0 20px 15px 20px;
    pointer-events: none;
}

.action-btn {
    display: flex;
    align-items: center;
    justify-content: center;
    text-decoration: none;
    transition: all 0.3s ease;
    pointer-events: auto;
    opacity: 0.7;
}

.action-btn i {
    font-size: 28px;
    transition: all 0.3s ease;
}

.action-btn:hover {
    opacity: 1;
    transform: translateY(-2px) scale(1.1);
}

/* X (Twitter) Button */
.x-btn {
    color: var(--fortune-gold);
}

.x-btn:hover {
    color: #fff;
    text-shadow: 0 0 10px var(--fortune-gold);
}

/* Telegram Button */
.tg-btn {
    color: var(--neon-blue);
}

.tg-btn:hover {
    color: #fff;
    text-shadow: 0 0 10px var(--neon-blue);
}

@keyframes pulse-border {
    0%, 100% {
        transform: scale(1);
        opacity: 0.6;
    }
    50% {
        transform: scale(1.05);
        opacity: 1;
    }
}

/* Tokenomics */
.tokenomics-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 30px;
    margin: 30px auto;
    max-width: 1200px;
    padding: 0 20px;
}

.token-info {
    background: rgba(10, 31, 26, 0.6);
    padding: 40px 30px;
    border-radius: 20px;
    text-align: center;
    transition: all 0.3s ease;
}

.token-info:hover {
    transform: translateY(-5px);
    box-shadow: 0 12px 40px rgba(46, 204, 113, 0.15);
}

.token-info h3 {
    color: var(--primary-color);
    margin-bottom: 10px;
}



/* Loading state */
.loading {
    opacity: 0.6;
    animation: pulse 1.5s ease-in-out infinite;
}

@keyframes pulse {
    0%, 100% { opacity: 0.6; }
    50% { opacity: 1; }
}

/* Updated state animation */
.updated {
    animation: glow 0.5s ease-in-out;
}

@keyframes glow {
    0% {
        transform: scale(1);
        text-shadow: 0 0 5px var(--primary-color);
    }
    50% {
        transform: scale(1.05);
        text-shadow: 0 0 15px var(--primary-color);
    }
    100% {
        transform: scale(1);
        text-shadow: 0 0 5px var(--primary-color);
    }
}

/* Price change indicators */
.price-up {
    color: #00ff88;
    font-weight: bold;
}

.price-down {
    color: #ff4444;
    font-weight: bold;
}

/* Responsive grid for tokenomics */
@media (max-width: 768px) {
    .tokenomics-grid {
        grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
        gap: 20px;
    }

    .token-info {
        padding: 25px 20px;
    }
}

/* Roadmap */
.timeline {
    max-width: 1000px;
    margin: 30px auto;
    padding: 0 30px;
}

.phase {
    background: rgba(10, 31, 26, 0.6);
    padding: 40px;
    border-radius: 20px;
    margin-bottom: 40px;
    position: relative;
}

.phase::after {
    content: '🍀';
    position: absolute;
    bottom: 10px;
    right: 10px;
    font-size: 20px;
    opacity: 0.2;
}

.phase:hover {
    transform: translateY(-5px);
    box-shadow: 0 12px 40px rgba(46, 204, 113, 0.15);
}

.phase:hover::after {
    animation: float 3s ease-in-out infinite;
}

.phase h3 {
    background: var(--gradient-primary);
    -webkit-background-clip: text;
    background-clip: text;
    color: transparent;
    margin-bottom: 20px;
}

.phase ul {
    list-style: none;
}

.phase ul li {
    margin-bottom: 10px;
    padding-left: 20px;
    position: relative;
}

.phase ul li::before {
    content: "•";
    color: var(--primary-color);
    position: absolute;
    left: 0;
}

/* Footer */
footer {
    background: rgba(45, 52, 54, 0.95);
    padding: 40px 0;
    margin-top: 40px;
    position: relative;
}

.social-links {
    display: flex;
    justify-content: center;
    gap: 30px;
    margin-bottom: 40px;
}

.social-links a {
    color: var(--primary-color);
    font-size: 24px;
    margin: 0 10px;
    transition: all 0.3s ease;
}

.social-links a:hover {
    color: var(--secondary-color);
    transform: translateY(-3px);
}

/* Responsive Design */
@media (max-width: 1200px) {
    .title-container h1 {
        font-size: 30px;
    }
    
    .hero .subtitle {
        font-size: 15px;
        max-width: 600px;
    }
}

@media (max-width: 768px) {
    .container {
        padding: 0 20px;
    }
    
    .nav-links {
        display: none;
    }
    
    .hero {
        padding: 100px 0 60px;
        min-height: 100vh;
    }
    
    .title-container h1 {
        font-size: 24px;
        line-height: 1.6;
        margin-bottom: 20px;
    }
    
    .hero .subtitle {
        font-size: 14px;
        margin-bottom: 20px;
        max-width: 100%;
        padding: 0 15px;
    }
    
    .contract-address {
        padding: 16px 24px;
        gap: 12px;
        flex-wrap: nowrap;
    }

    #contract-address {
        font-size: 10px;
        word-break: break-all;
        text-align: center;
    }

    .copy-btn {
        min-width: 34px;
        height: 34px;
        padding: 8px;
    }
    
    .cta-buttons {
        display: flex;
        flex-direction: column;
        align-items: stretch;
        gap: 24px;
        padding: 0 20px;
        margin-top: 32px;
        margin-bottom: 32px;
        width: 100%;
    }
    
    .btn {
        width: 100%;
        height: 52px;
        font-size: 15px;
        border-radius: 100px;
        letter-spacing: 1.2px;
        min-width: unset;
    }

    .btn.secondary {
        height: 52px;
    }

    /* Mobile responsive for action buttons */
    .feature-actions {
        padding: 0 16px 12px 16px;
    }

    .action-btn i {
        font-size: 24px;
    }

    /* Add touch-specific improvements */
    @media (hover: none) {
        .btn {
            transition: transform 0.3s ease, box-shadow 0.3s ease;
        }

        .btn:active {
            transform: scale(0.98);
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
        }

        .action-btn:active {
            transform: scale(0.95);
        }
    }
}

@media (max-width: 480px) {
    .hero {
        padding: 80px 0 40px;
    }
    
    .title-container h1 {
        font-size: 20px;
        line-height: 1.8;
    }
    
    .hero .subtitle {
        font-size: 12px;
        line-height: 1.8;
    }
    
    .contract-address {
        padding: 14px 20px;
        margin: 20px 10px;
    }
    
    #contract-address {
        font-size: 9px;
    }
    
    .cta-buttons {
        gap: 20px;
        padding: 0 16px;
        margin-top: 28px;
        margin-bottom: 28px;
    }
    
    .btn {
        height: 48px;
        font-size: 14px;
        border-radius: 100px;
    }

    .btn.secondary {
        height: 48px;
    }
}

@media (max-height: 600px) {
    .hero {
        padding: 60px 0 30px;
        min-height: 120vh;
    }
}

@media (orientation: landscape) and (max-height: 600px) {
    .hero {
        padding: 80px 0 40px;
        min-height: 120vh;
    }
    
    .title-container h1 {
        font-size: 22px;
        margin-bottom: 15px;
    }
    
    .hero .subtitle {
        margin-bottom: 15px;
    }
    
    .cta-buttons {
        flex-direction: row;
        justify-content: center;
        gap: 20px;
        padding: 0 24px;
    }
    
    .btn {
        width: auto;
        min-width: 180px;
    }
}

/* Add clover animation keyframes */
@keyframes float {
    0% {
        transform: translateY(0px);
    }
    25% {
        transform: translateY(-10px) rotate(1deg);
    }
    50% {
        transform: translateY(0px);
    }
    75% {
        transform: translateY(-5px) rotate(-1deg);
    }
    100% {
        transform: translateY(0px);
    }
}

@keyframes lucky-pulse {
    0% { box-shadow: 0 0 0 0 rgba(46, 204, 113, 0.4); }
    70% { box-shadow: 0 0 0 10px rgba(46, 204, 113, 0); }
    100% { box-shadow: 0 0 0 0 rgba(46, 204, 113, 0); }
}

/* Floating Background Clovers */
.floating-clovers {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100vh;
    pointer-events: none;
    z-index: 1;
    overflow: hidden;
}

.clover {
    position: absolute;
    font-size: 24px;
    opacity: var(--clover-opacity, 0.2);
    will-change: transform;
    transform: translateZ(0);
    backface-visibility: hidden;
    animation: float-clover var(--duration) linear forwards;
    animation-delay: var(--delay);
    z-index: 2;
    bottom: -20px;
    left: var(--start-x);
}

.clover.large {
    font-size: 48px;
    opacity: 0.22;
    filter: blur(0.5px) drop-shadow(0 0 4px rgba(46, 204, 113, 0.35));
}

.clover.small {
    font-size: 28px;
    opacity: 0.28;
    filter: blur(0.5px) drop-shadow(0 0 2px rgba(46, 204, 113, 0.25));
}

@keyframes float-clover {
    0% {
        transform: translateY(0) rotate(0deg);
        opacity: 0;
    }
    10% {
        opacity: var(--clover-opacity);
        transform: translateY(-20px) rotate(5deg);
    }
    90% {
        opacity: var(--clover-opacity);
        transform: translateY(calc(-100vh + 40px)) rotate(355deg) translateX(var(--drift-x));
    }
    100% {
        transform: translateY(-100vh) rotate(360deg) translateX(var(--drift-x));
        opacity: 0;
    }
}

/* Update hero section to work with background */
.hero {
    position: relative;
    z-index: 1;
    background: linear-gradient(135deg, rgba(26, 182, 79, 0.1) 0%, rgba(46, 204, 113, 0.1) 100%);
}

@keyframes rainbow-text {
    0% { filter: hue-rotate(0deg); }
    100% { filter: hue-rotate(360deg); }
}

@keyframes holographic-shift {
    0% { background-position: 0% 0%; }
    50% { background-position: 100% 100%; }
    100% { background-position: 0% 0%; }
}

@keyframes neon-pulse {
    0%, 100% { text-shadow: 0 0 10px var(--neon-blue),
                           0 0 20px var(--neon-blue),
                           0 0 30px var(--neon-blue); }
    50% { text-shadow: 0 0 20px var(--neon-blue),
                      0 0 30px var(--neon-blue),
                      0 0 40px var(--neon-blue),
                      0 0 50px var(--neon-blue); }
}

/* Add floating meme elements */
.floating-memes {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    pointer-events: none;
    z-index: 1;
}

.meme {
    position: absolute;
    width: 40px;
    height: 40px;
    background-size: contain;
    background-repeat: no-repeat;
    opacity: 0.8;
    animation: float-meme 3s ease-in-out infinite;
}

.meme.doge { background-image: url('data:image/svg+xml,<svg>...</svg>'); }
.meme.pepe { background-image: url('data:image/svg+xml,<svg>...</svg>'); }
.meme.stonks { background-image: url('data:image/svg+xml,<svg>...</svg>'); }

@keyframes float-meme {
    0%, 100% { transform: translateY(0) rotate(0); }
    50% { transform: translateY(-20px) rotate(10deg); }
}

/* Add glitch effect */
.glitch {
    position: relative;
    animation: text-flicker 5s infinite;
}

.glitch span:first-child {
    background: linear-gradient(
        45deg,
        var(--text-color) 60%,
        var(--rainbow-2) 70%,
        var(--rainbow-3) 80%,
        var(--rainbow-4) 90%
    );
    -webkit-background-clip: text;
    background-clip: text;
    color: transparent;
    text-shadow: 1px 1px rgba(255, 255, 255, 0.4);
}

/* Special styling for $LUCK */
.glitch span:first-child span.luck-text {
    background: var(--fortune-gold);
    -webkit-background-clip: text;
    background-clip: text;
    color: transparent;
    text-shadow: 1px 1px rgba(0, 0, 0, 0.3);
    animation: luck-pulse 3s infinite;
}

.glitch::before,
.glitch::after {
    content: attr(data-text);
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: transparent;
    opacity: 0.1;
}

.glitch::before {
    animation: glitch-effect 4s infinite;
    clip-path: polygon(0 0, 100% 0, 100% 45%, 0 45%);
    text-shadow: -1px 0 var(--rainbow-1);
    transform: translate(-2px, 2px);
}

.glitch::after {
    animation: glitch-effect 3s infinite reverse;
    clip-path: polygon(0 60%, 100% 60%, 100% 100%, 0 100%);
    text-shadow: 1px 0 var(--rainbow-4);
    transform: translate(2px, -2px);
}

@keyframes glitch-effect {
    0%, 100% {
        transform: translate(0);
    }
    20% {
        transform: translate(-2px, 1px);
    }
    40% {
        transform: translate(-2px, -1px);
    }
    60% {
        transform: translate(2px, 1px);
    }
    80% {
        transform: translate(2px, -1px);
    }
}

@keyframes luck-pulse {
    0%, 100% {
        filter: brightness(1) drop-shadow(0 0 5px var(--fortune-gold));
    }
    50% {
        filter: brightness(1.3) drop-shadow(0 0 8px var(--fortune-gold));
    }
}

@keyframes text-flicker {
    0%, 100% {
        opacity: 1;
    }
    5%, 95% {
        opacity: 0.95;
    }
    10%, 90% {
        opacity: 1;
    }
    15%, 85% {
        opacity: 0.95;
    }
    20%, 80% {
        opacity: 1;
    }
}

/* Rainbow border effect */
.rainbow-border {
    position: relative;
    border: none;
    background: transparent;
    box-shadow: 0 0 30px rgba(24, 220, 255, 0.3);
}

.rainbow-border::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(45deg, 
        var(--rainbow-1),
        var(--rainbow-2),
        var(--rainbow-3),
        var(--rainbow-4)
    );
    opacity: 0.1;
    border-radius: inherit;
    z-index: -1;
    animation: border-rotate 4s linear infinite;
}

@keyframes border-rotate {
    0% {
        border-image: linear-gradient(0deg, 
            var(--rainbow-1),
            var(--rainbow-2),
            var(--rainbow-3),
            var(--rainbow-4)
        ) 1;
    }
    100% {
        border-image: linear-gradient(360deg,
            var(--rainbow-1),
            var(--rainbow-2),
            var(--rainbow-3),
            var(--rainbow-4)
        ) 1;
    }
}

/* Add sparkle effects */
.sparkle {
    position: relative;
}

.sparkle::after {
    content: '✨';
    position: absolute;
    animation: sparkle-float 3s ease infinite;
}

@keyframes sparkle-float {
    0%, 100% {
        transform: translate(0, 0) rotate(0deg);
        opacity: 1;
    }
    50% {
        transform: translate(10px, -10px) rotate(180deg);
        opacity: 0.5;
    }
}

/* Add retro game effects */
.pixel-border {
    box-shadow: 0 0 20px rgba(24, 220, 255, 0.3);
}

/* Add coin flip animation */
.coin {
    position: relative;
    display: inline-block;
    animation: coin-flip 2s ease-in-out infinite;
    transform-style: preserve-3d;
}

@keyframes coin-flip {
    0%, 100% {
        transform: rotateY(0deg);
    }
    50% {
        transform: rotateY(180deg);
    }
}

/* Add matrix rain effect */
.matrix-bg {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    pointer-events: none;
    z-index: 0;
    opacity: 0.05;
    background: linear-gradient(0deg,
        var(--lucky-green) 25%,
        var(--neon-blue) 50%,
        transparent 75%
    );
    will-change: transform;
    transform: translateZ(0);
    backface-visibility: hidden;
    animation: matrix-rain 20s linear infinite;
}

@keyframes matrix-rain {
    0% {
        background-position: 0% 0%;
    }
    100% {
        background-position: 0% 1000%;
    }
}

/* Add hover card effect */
.hover-card {
    transition: all 0.3s ease;
    transform-style: preserve-3d;
    perspective: 1000px;
}

.hover-card:not(.contract-address):hover {
    transform: translateY(-5px) scale(1.02);
    box-shadow: 0 8px 30px rgba(26, 255, 156, 0.3);
    border-color: var(--lucky-green);
}

/* Add lucky charm particles */
.lucky-particles {
    position: absolute;
    width: 100%;
    height: 100%;
    top: 0;
    left: 0;
    pointer-events: none;
    z-index: 1;
    opacity: 0.6;
}

.lucky-particle {
    position: absolute;
    width: 10px;
    height: 10px;
    background: var(--fortune-gold);
    border-radius: 50%;
    animation: particle-float 4s ease-in-out infinite;
    will-change: transform, opacity;
    transform: translateZ(0);
    backface-visibility: hidden;
}

@keyframes particle-float {
    0%, 100% {
        transform: translate3d(0, 0, 0) scale(1);
        opacity: 0;
    }
    50% {
        transform: translate3d(var(--x, 20px), var(--y, -20px), 0) scale(1.5);
        opacity: 1;
    }
} 